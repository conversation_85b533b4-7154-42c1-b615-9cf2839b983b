package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb

import adhoc.helper.LogAppenderHelper
import com.decurret_dcp.dcjpy.bcmonitoring.config.DynamoDBConnectionPool
import java.util.function.Function
import org.apache.logging.log4j.Level
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException
import spock.lang.Specification

class PooledDynamoDbServiceSpec extends Specification {

	DynamoDBConnectionPool mockConnectionPool
    LogAppenderHelper logAppender
	PooledDynamoDbService pooledDynamoDbService

	def setup() {
		mockConnectionPool = Mock(DynamoDBConnectionPool)
        logAppender = LogAppenderHelper.createAndAttach(PooledDynamoDbService.class.name)
		pooledDynamoDbService = new PooledDynamoDbService(mockConnectionPool)
	}

    def cleanup() {
        // Remove log appender to prevent log pollution between tests
        if (logAppender != null) {
            // Detach from all loggers that might be using this appender
            logAppender.detach()
            logAppender = null
        }
    }

	def "should successfully execute operation with connection"() {
		given:
		def mockClient = Mock(DynamoDbClient)
		def operation = Mock(Function)
		def expectedResult = "test-result"

		when:
		def result = pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> mockClient
		1 * operation.apply(mockClient) >> expectedResult
		1 * mockConnectionPool.releaseConnection(mockClient)
		logAppender.events.findAll(it -> it.level == Level.ERROR).size() == 0
		result == expectedResult
	}

	def "should handle InterruptedException during connection acquisition"() {
		given:
		def operation = Mock(Function)
		def interruptedException = new InterruptedException("Connection interrupted")

		when:
		pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> { throw interruptedException }
		0 * operation.apply(_)
		0 * mockConnectionPool.releaseConnection(_)
        logAppender.events.findAll(it -> it.level == Level.ERROR).size() == 0

		def thrownException = thrown(RuntimeException)
		thrownException.message == "Thread interrupted while acquiring DynamoDB connection"
		thrownException.cause == interruptedException
		Thread.currentThread().isInterrupted()
	}

	def "should handle DynamoDbException during operation execution"() {
		given:
		def mockClient = Mock(DynamoDbClient)
		def operation = Mock(Function)
		def dynamoDbException = DynamoDbException.builder()
				.message("DynamoDB operation failed")
				.build()

		when:
		pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> mockClient
		1 * operation.apply(mockClient) >> { throw dynamoDbException }
		logAppender.events.message.formattedMessage.contains("DynamoDB operation failed")
		1 * mockConnectionPool.releaseConnection(mockClient)

		def thrownException = thrown(DynamoDbException)
		thrownException == dynamoDbException
	}

	def "should handle generic Exception during operation execution"() {
		given:
		def mockClient = Mock(DynamoDbClient)
		def operation = Mock(Function)
		def genericException = new RuntimeException("Generic error")

		when:
		pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> mockClient
		1 * operation.apply(mockClient) >> { throw genericException }
        logAppender.events.message.formattedMessage.contains("Unexpected error during DynamoDB operation")
		1 * mockConnectionPool.releaseConnection(mockClient)

		def thrownException = thrown(RuntimeException)
		thrownException.message == "DynamoDB operation failed"
		thrownException.cause == genericException
	}

	def "should release connection even when operation throws exception"() {
		given:
		def mockClient = Mock(DynamoDbClient)
		def operation = Mock(Function)
		def exception = new RuntimeException("Operation failed")

		when:
		pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> mockClient
		1 * operation.apply(mockClient) >> { throw exception }
		1 * mockConnectionPool.releaseConnection(mockClient)
        logAppender.events.message.formattedMessage.contains("Unexpected error during DynamoDB operation")
		thrown(RuntimeException)
	}

	def "should not release connection when acquisition fails"() {
		given:
		def operation = Mock(Function)
		def acquisitionException = new RuntimeException("Failed to acquire connection")

		when:
		pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> { throw acquisitionException }
		0 * operation.apply(_)
		0 * mockConnectionPool.releaseConnection(_)
        logAppender.events.message.formattedMessage.contains("Unexpected error during DynamoDB operation")

		def thrownException = thrown(RuntimeException)
		thrownException.message == "DynamoDB operation failed"
		thrownException.cause == acquisitionException
	}

	def "should handle null client gracefully in finally block"() {
		given:
		def operation = Mock(Function)
		def acquisitionException = new RuntimeException("Failed to acquire connection")

		when:
		pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> { throw acquisitionException }
		0 * operation.apply(_)
		0 * mockConnectionPool.releaseConnection(_) // Should not be called with null client
        logAppender.events.message.formattedMessage.contains("Unexpected error during DynamoDB operation")
		thrown(RuntimeException)
	}

	def "should execute operation with proper return type"() {
		given:
		def mockClient = Mock(DynamoDbClient)
		def operation = { DynamoDbClient client ->
			return 42L // Return Long type
		}

		when:
		Long result = pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> mockClient
		1 * mockConnectionPool.releaseConnection(mockClient)
        logAppender.events.findAll(it -> it.level == Level.ERROR).size() == 0
		result == 42L
		result instanceof Long
	}

	def "should execute operation that returns null"() {
		given:
		def mockClient = Mock(DynamoDbClient)
		def operation = { DynamoDbClient client ->
			return null
		}

		when:
		def result = pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> mockClient
		1 * mockConnectionPool.releaseConnection(mockClient)
        logAppender.events.findAll(it -> it.level == Level.ERROR).size() == 0
		result == null
	}

	def "should preserve thread interruption status when InterruptedException occurs"() {
		given:
		def operation = Mock(Function)
		def interruptedException = new InterruptedException("Thread interrupted")
		// Clear any existing interruption status
		Thread.interrupted()

		when:
		pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> { throw interruptedException }
		0 * operation.apply(_)
		0 * mockConnectionPool.releaseConnection(_)

		def thrownException = thrown(RuntimeException)
		thrownException.message == "Thread interrupted while acquiring DynamoDB connection"
		thrownException.cause == interruptedException
		// Verify thread interruption status is preserved
		Thread.currentThread().isInterrupted()

		cleanup:
		// Clear interruption status for other tests
		Thread.interrupted()
	}

	def "should handle multiple operations with same service instance"() {
		given:
		def mockClient1 = Mock(DynamoDbClient)
		def mockClient2 = Mock(DynamoDbClient)
		def operation1 = { DynamoDbClient client -> "result1" }
		def operation2 = { DynamoDbClient client -> "result2" }

		when:
		def result1 = pooledDynamoDbService.executeWithConnection(operation1)
		def result2 = pooledDynamoDbService.executeWithConnection(operation2)

		then:
		1 * mockConnectionPool.acquireConnection() >> mockClient1
		1 * mockConnectionPool.releaseConnection(mockClient1)
		1 * mockConnectionPool.acquireConnection() >> mockClient2
		1 * mockConnectionPool.releaseConnection(mockClient2)
        logAppender.events.findAll(it -> it.level == Level.ERROR).size() == 0
		result1 == "result1"
		result2 == "result2"
	}

	def "should handle operation that modifies client state"() {
		given:
		def mockClient = Mock(DynamoDbClient)
		def operation = { DynamoDbClient client ->
			// Simulate operation that might modify client state
			client.toString() // Just to interact with the client
			return "operation-completed"
		}

		when:
		def result = pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> mockClient
		1 * mockClient.toString() >> "mock-client"
		1 * mockConnectionPool.releaseConnection(mockClient)
        logAppender.events.findAll(it -> it.level == Level.ERROR).size() == 0
		result == "operation-completed"
	}

	def "should handle nested exception in operation"() {
		given:
		def mockClient = Mock(DynamoDbClient)
		def rootCause = new IllegalArgumentException("Root cause")
		def wrappedException = new RuntimeException("Wrapped exception", rootCause)
		def operation = Mock(Function)

		when:
		pooledDynamoDbService.executeWithConnection(operation)

		then:
		1 * mockConnectionPool.acquireConnection() >> mockClient
		1 * operation.apply(mockClient) >> { throw wrappedException }
		logAppender.events.message.formattedMessage.contains("Unexpected error during DynamoDB operation")
		1 * mockConnectionPool.releaseConnection(mockClient)

		def thrownException = thrown(RuntimeException)
		thrownException.message == "DynamoDB operation failed"
		thrownException.cause == wrappedException
		thrownException.cause.cause == rootCause
	}
}
