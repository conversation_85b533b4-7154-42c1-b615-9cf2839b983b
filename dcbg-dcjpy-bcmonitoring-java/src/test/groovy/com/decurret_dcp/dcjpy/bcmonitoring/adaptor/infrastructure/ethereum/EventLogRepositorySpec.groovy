package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum

import adhoc.helper.LogAppenderHelper
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction
import com.decurret_dcp.dcjpy.bcmonitoring.exception.BlockchainException
import java.util.concurrent.LinkedBlockingQueue
import org.apache.logging.log4j.Level
import org.apache.logging.log4j.Logger
import spock.lang.Specification

class EventLogRepositorySpec extends Specification {

    LogAppenderHelper logAppender
	EthEventLogDao mockEventLogDao
	EventLogRepository repository

	def setup() {
        logAppender = LogAppenderHelper.createAndAttach(EventLogRepositoryImpl.class.name)
		mockEventLogDao = Mock(EthEventLogDao)
		repository = new EventLogRepositoryImpl(mockEventLogDao)
	}

    def cleanup() {
        // Remove log appender to prevent log pollution between tests
        if (logAppender != null) {
            // Detach from all loggers that might be using this appender
            logAppender.detach()
            logAppender = null
        }
    }

	def "Subscribe should return transactions when successful"() {
		given:
		def transactions = new LinkedBlockingQueue<Transaction>()

		when:
		def result = repository.subscribe()

		then:
		1 * mockEventLogDao.subscribeAll() >> transactions
		result == transactions
	}

	def "Subscribe should throw BlockchainException when DAO error occurs"() {
		given:
		def mockException = new RuntimeException("mock error")

		when:
		repository.subscribe()

		then:
		1 * mockEventLogDao.subscribeAll() >> { throw mockException }
        logAppender.events.findAll(it -> it.level == Level.ERROR).size() == 1
		def exception = thrown(BlockchainException)
		exception.cause == mockException
	}

	def "GetFilterLogs should return transactions when successful"() {
		given:
		def blockHeight = 1000L
		def transactions = new ArrayList<Transaction>()

		when:
		def result = repository.getFilterLogs(blockHeight)

		then:
		1 * mockEventLogDao.getPendingTransactions(blockHeight) >> transactions
		result == transactions
	}

	def "GetFilterLogs should throw BlockchainException when DAO error occurs"() {
		given:
		def blockHeight = 1000L
		def mockException = new IOException("mock error")

		when:
		repository.getFilterLogs(blockHeight)

		then:
		1 * mockEventLogDao.getPendingTransactions(blockHeight) >> { throw mockException }
        logAppender.events.findAll(it -> it.level == Level.ERROR).size() == 1
		def exception = thrown(BlockchainException)
		exception.cause == mockException
	}
}
