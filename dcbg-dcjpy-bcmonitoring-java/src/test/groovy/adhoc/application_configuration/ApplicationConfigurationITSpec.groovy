package adhoc.application_configuration

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class ApplicationConfigurationITSpec extends BaseAdhocITSpec {

	@Autowired
	Web3jConfig web3jConfig

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	@Autowired
	ApplicationContext applicationContext

	@Autowired
	BcmonitoringConfigurationProperties properties

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"Token",
			"Account",
			"Provider"
		])

		// Setup mock event stream and pending event
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Successful load all configuration properties correctly to run application
	 * Verifies all configuration properties correctly
	 * Expected: Service logs "Started bc monitoring"
	 */
	def "Should load all configuration properties correctly"() {
		given: "Running application with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		when: "Command line runner is executed"
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "Configuration properties are loaded correctly"
		properties.getAws().getRegion() == "ap-northeast-1"
		properties.getAws().getAccessKeyId() == "test-access-key"
		properties.getAws().getSecretAccessKey() == "test-secret-key"
		properties.getAws().getDynamodb().getRegion() == "ap-northeast-1"
		properties.getAws().getDynamodb().getEndpoint() == "http://localhost:4566"
		properties.getAws().getS3().getBucketName() == "abijson-local-bucket"
		properties.getWebsocket().getUri().getHost() == "localhost"
		properties.getWebsocket().getUri().getPort() == "18541"
		properties.getSubscription().getCheckInterval() == "3000"
		properties.getSubscription().getAllowableBlockTimestampDiffSec() == "2"
		properties.getEnv() == "local"
		properties.getAbiFormat() == "hardhat"
		//this properties != true cuz when run application in test need set this value == false to avoid infinite loop
		!properties.isEagerStart()

		and: "Log should indicate bc monitoring has started"
		def messages = logAppender.events.message.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
	}

	/**
	 * Successfully initializes application with env == 'test'
	 * Verifies that the ENV variable value is 'test'
	 * Expected: Property values are loaded from application-test.properties
	 */
	def "Should load all configuration properties correctly when env is 'test'"() {
		when: "initialize application"

		then: "env should be 'test'"
		properties.getEnv() == "local"
		properties.getAws().getAccessKeyId() == "test-access-key"
		properties.getAws().getSecretAccessKey() == "test-secret-key"
	}

	/**
	 * Run application fails with invalid subscription check interval value
	 * Verifies that the application failed
	 * Expected: Service logs "Failed to convert checkInterval"
	 */
	def "Should handle invalid subscription check interval value gracefully"() {
		given: "Invalid system property is set"
		properties.getSubscription().setCheckInterval("invalid")

		and: "Application is started via command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		when: "Run command line with -f"
		scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 5, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then:
		def messages = logAppender.events.message.formattedMessage
		assert messages.any { it.contains("Failed to convert checkInterval:") }

		cleanup:
		properties.getSubscription().setCheckInterval("3000")
	}

	/**
	 * Run application fails with invalid subscription allowable block timestamp diff sec value
	 * Verifies that the subscription allowable block timestamp diff sec value can't parse
	 * Expected: Service logs "Failed to parse allowable timestamp difference"
	 */
	def "Should handle invalid subscription allowable block timestamp diff sec value gracefully"() {
		given: "Invalid system property is set"
		properties.getSubscription().setAllowableBlockTimestampDiffSec("invalid")

		and: "Application is started via command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		when: "Run command line with -f"
		scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 5, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then:
		def messages = logAppender.events.message.formattedMessage
		assert messages.any { it.contains("Failed to parse allowable timestamp difference") }

		cleanup:
		properties.getSubscription().setAllowableBlockTimestampDiffSec("2")
	}
}
