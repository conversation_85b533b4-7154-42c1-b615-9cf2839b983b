<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">

    <!-- Properties -->
    <Properties>
        <Property name="APP_NAME">bcmonitoring</Property>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ} [%t] %-5level %C{1}:%L %logger{36} [%X{process_uuid}] - %msg%n</Property>
        <Property name="LOG_JSON_PATTERN">{"time":"%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ}","level":"%level","file":"%class:%line","message":"%msg","context":%X,"application":"${APP_NAME}"}%n</Property>
        <Property name="LOG_FILE_TEXT">logs/app.log</Property>
        <Property name="LOG_FILE_JSON">logs/app.json</Property>
    </Properties>

    <!-- Appenders -->
    <Appenders>
        <!-- Console Text -->
        <Console name="ConsoleText" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}" />
        </Console>

        <!-- Console JSON -->
        <Console name="ConsoleJson" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_JSON_PATTERN}">
            </PatternLayout>
        </Console>

        <!-- File Text with Rolling -->
        <RollingFile name="FileText" fileName="${LOG_FILE_TEXT}" filePattern="logs/app.%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="${LOG_PATTERN}" />
            <Policies>
                <TimeBasedTriggeringPolicy />
            </Policies>
            <DefaultRolloverStrategy max="7"/>
        </RollingFile>

        <!-- File JSON with Rolling -->
        <RollingFile name="FileJson" fileName="${LOG_FILE_JSON}" filePattern="logs/app.%d{yyyy-MM-dd}.json">
            <JsonLayout eventEol="true" compact="true" includeStacktrace="true" objectMessageAsJsonObject="true" properties="true">
                <KeyValuePair key="application" value="${APP_NAME}"/>
            </JsonLayout>
            <Policies>
                <TimeBasedTriggeringPolicy />
            </Policies>
            <DefaultRolloverStrategy max="7"/>
        </RollingFile>
    </Appenders>

    <!-- Loggers -->
    <Loggers>
        <!-- Application-specific logging -->
        <Logger name="com.decurret_dcp.dcjpy" level="debug" additivity="false">
            <AppenderRef ref="ConsoleJson"/>
            <AppenderRef ref="FileJson"/>
        </Logger>

        <!-- Reduce verbosity of common loggers -->
        <Logger name="org.springframework" level="info"/>
        <Logger name="software.amazon.awssdk" level="info"/>

        <!-- Root logger -->
        <Root level="info">
            <AppenderRef ref="ConsoleJson"/>
            <AppenderRef ref="FileJson"/>
        </Root>
    </Loggers>
</Configuration>
