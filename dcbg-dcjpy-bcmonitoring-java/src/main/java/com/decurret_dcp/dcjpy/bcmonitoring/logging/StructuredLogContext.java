package com.decurret_dcp.dcjpy.bcmonitoring.logging;

import java.util.HashMap;
import java.util.Map;
import org.apache.logging.log4j.ThreadContext;

/**
 * A utility class for managing structured logging context. This class provides a fluent API for
 * adding context data to logs and automatically cleans up the context when the try-with-resources
 * block ends.
 */
public class StructuredLogContext implements AutoCloseable {
  private final Map<String, String> contextValues = new HashMap<>();

  private StructuredLogContext(Map<String, Object> values) {
    // Add all values to MDC
    values.forEach(
        (key, value) -> {
          String stringValue = value != null ? String.valueOf(value) : null;
          ThreadContext.put(key, stringValue);
          contextValues.put(key, stringValue);
        });
  }

  /**
   * Creates a new builder for constructing a StructuredLogContext.
   *
   * @return a new Builder instance
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Creates a new StructuredLogContext with the specified context data. This is a convenience
   * method for simple cases.
   *
   * @param key the context key
   * @param value the context value
   * @return a new StructuredLogContext
   */
  public static StructuredLogContext of(String key, Object value) {
    return builder().put(key, value).build();
  }

  /**
   * Creates a new StructuredLogContext with the specified context data. This is a convenience
   * method for simple cases.
   *
   * @param key1 the first context key
   * @param value1 the first context value
   * @param key2 the second context key
   * @param value2 the second context value
   * @return a new StructuredLogContext
   */
  public static StructuredLogContext of(String key1, Object value1, String key2, Object value2) {
    return builder().put(key1, value1).put(key2, value2).build();
  }

  /**
   * Creates a new StructuredLogContext with the specified context data. This is a convenience
   * method for blockchain event context.
   *
   * @param eventName the event name
   * @param txHash the transaction hash
   * @param blockHeight the block height
   * @param logIndex the log index
   * @param blockTimestamp the block timestamp
   * @param traceId the trace ID
   * @return a new StructuredLogContext
   */
  public static StructuredLogContext forBlockchainEvent(
      String eventName,
      String txHash,
      long blockHeight,
      int logIndex,
      long blockTimestamp,
      String traceId) {
    return builder()
        .put("event_name", eventName)
        .put("tx_hash", txHash)
        .put("block_height", blockHeight)
        .put("log_index", logIndex)
        .put("block_timestamp", blockTimestamp)
        .put("trace_id", traceId)
        .build();
  }

  /** Removes all context values from MDC when the try-with-resources block ends. */
  @Override
  public void close() {
    // Remove all values from MDC
    contextValues.keySet().forEach(ThreadContext::remove);
  }

  /** Builder for constructing a StructuredLogContext. */
  public static class Builder {
    private final Map<String, Object> values = new HashMap<>();

    /**
     * Adds a context value to the builder.
     *
     * @param key the context key
     * @param value the context value
     * @return this builder
     */
    public Builder put(String key, Object value) {
      values.put(key, value);
      return this;
    }

    /**
     * Adds all entries from the specified map to the builder.
     *
     * @param contextData the map of context data
     * @return this builder
     */
    public Builder putAll(Map<String, Object> contextData) {
      if (contextData != null) {
        values.putAll(contextData);
      }
      return this;
    }

    /**
     * Builds a new StructuredLogContext with the values added to this builder.
     *
     * @return a new StructuredLogContext
     */
    public StructuredLogContext build() {
      return new StructuredLogContext(values);
    }
  }
}
