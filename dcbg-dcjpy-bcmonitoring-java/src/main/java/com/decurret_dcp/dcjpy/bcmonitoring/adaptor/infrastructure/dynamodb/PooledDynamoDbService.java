package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb;

import com.decurret_dcp.dcjpy.bcmonitoring.config.DynamoDBConnectionPool;
import java.util.function.Function;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;

/**
 * Service that provides pooled DynamoDB operations This service manages connection acquisition and
 * release automatically
 */
@Service
@Log4j2
public class PooledDynamoDbService {

  private final DynamoDBConnectionPool connectionPool;

  public PooledDynamoDbService(DynamoDBConnectionPool connectionPool) {
    this.connectionPool = connectionPool;
  }

  /**
   * Execute a DynamoDB operation with automatic connection management
   *
   * @param operation Function that takes DynamoDbClient and returns result
   * @param <T> Return type of the operation
   * @return Result of the operation
   * @throws DynamoDbException if DynamoDB operation fails
   * @throws RuntimeException if connection management fails
   */
  public <T> T executeWithConnection(Function<DynamoDbClient, T> operation) {
    DynamoDbClient client = null;
    try {
      // Acquire connection from pool
      client = connectionPool.acquireConnection();

      // Execute the operation
      return operation.apply(client);

    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new RuntimeException("Thread interrupted while acquiring DynamoDB connection", e);
    } catch (DynamoDbException e) {
      log.error("DynamoDB operation failed", e);
      throw e;
    } catch (Exception e) {
      log.error("Unexpected error during DynamoDB operation", e);
      throw new RuntimeException("DynamoDB operation failed", e);
    } finally {
      // Always release connection back to pool
      if (client != null) {
        connectionPool.releaseConnection(client);
      }
    }
  }
}
