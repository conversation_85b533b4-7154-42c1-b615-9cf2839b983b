name: Unit Test

on:
  - pull_request

jobs:
  run_unit_test:
    name: Run unit test
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: 'corretto'

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_ARN }}
          aws-region: ap-northeast-1

# エラーになるため、コメントアウト
#      - uses: actions/cache@v3
#        with:
#          path: |
#            ~/.gradle/caches
#            ~/.gradle/wrapper
#          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
#          restore-keys: |
#            ${{ runner.os }}-gradle-
      - run: ./gradlew check
