@startuml

!theme cerulean

autonumber
participant BESU         as besu
participant BCMonitoring as bcm
database S3 as s3
database DynamoDB as Dynamo
activate bcm

group Xử lý khởi tạo
    bcm -> s3 : Lấy toàn bộ các file ABI JSON của tất cả zone dưới bucket
    note right
      ABI JSON đã tải xuống sẽ được lưu
      với khóa là địa chỉ contract,
      và giá trị là ContractEvents
      (tên contract và thông tin event)
      dưới dạng Key-Value
    end note
  end
    
    group Vòng lặp giám sát
        bcm -> Dynamo : Lấy Block height cuối cùng đã phát hiện sự kiện
        
        create control NewBlockSubscription as subscription
        bcm -> subscription : Subscribe để nhận thông báo block mới
        note right
          Sử dụng Web3j WebSocket để subscribe newHeadsNotifications  
          Các block mới sẽ được lưu vào queue bất đồng bộ
        end note
        
        bcm -> besu : <PERSON><PERSON><PERSON> to<PERSON><PERSON> bộ log của các block trong qu<PERSON> khứ (từ Block cuối +1 đến hiện tại)
        besu -> bcm : Tr<PERSON> về event log của các block quá khứ
        
        group Xử lý block quá khứ (xử lý tuần tự)
            loop Với mỗi Transaction trong block quá khứ
                alt Nếu là event có trong ABI JSON lấy từ S3
                    bcm -> Dynamo : Put bản ghi Event
                end
                alt Nếu Block height thay đổi
                    bcm -> Dynamo : Put bản ghi Block height
                end
            end
            bcm -> Dynamo : Put bản ghi Block height cuối cùng
            note right
              Chỉ bắt đầu xử lý block mới
              sau khi hoàn tất xử lý block quá khứ
            end note
        end

besu -> besu : emit event
note right
  Event mới sẽ được lưu vào queue
  của subscription một cách bất đồng bộ
end note

        group Xử lý block mới (xử lý liên tục)
            loop
                subscription -> bcm : Lấy Transaction mới từ queue (poll mỗi 5 giây)
                alt Nếu có Transaction
                    alt Nếu là event có trong ABI JSON lấy từ S3
                        bcm -> Dynamo : Put bản ghi Event
                        bcm -> Dynamo : Put bản ghi Block height
                    end
                end
            end
        end
        
        alt Khi xảy ra lỗi
            bcm -> subscription : Thực thi unsubscribe()
            bcm -> bcm : sleep trong khoảng checkInterval, sau đó restart BCMonitoring
        end
    end

deactivate bcm
@enduml
