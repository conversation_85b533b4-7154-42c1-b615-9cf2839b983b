{"address": "0xC1ab0fA32BC8Add3E7Ab12216d631F5734E04333", "abi": [{"type": "event", "anonymous": false, "name": "AddToken", "inputs": [{"type": "bytes32", "name": "tokenId", "indexed": true}, {"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}, {"type": "bool", "name": "enabled", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Approval", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "ownerId", "indexed": true}, {"type": "bytes32", "name": "spenderId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Burn", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "balance", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "BurnCancel", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "balance", "indexed": false}, {"type": "uint256", "name": "blockTimestamp", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "CustomTransfer", "inputs": [{"type": "bytes32", "name": "sendAccountId", "indexed": false}, {"type": "bytes32", "name": "fromAccountId", "indexed": false}, {"type": "bytes32", "name": "toAccountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "bytes32", "name": "miscValue1", "indexed": false}, {"type": "string", "name": "miscValue2", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Mint", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "balance", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ModToken", "inputs": [{"type": "bytes32", "name": "tokenId", "indexed": true}, {"type": "bytes32", "name": "name", "indexed": false}, {"type": "bytes32", "name": "symbol", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SetEnabledToken", "inputs": [{"type": "bytes32", "name": "tokenId", "indexed": true}, {"type": "bool", "name": "enabled", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Transfer", "inputs": [{"type": "tuple", "name": "transferData", "indexed": false, "components": [{"type": "bytes32", "name": "transferType"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "fromValidatorId"}, {"type": "bytes32", "name": "toValidatorId"}, {"type": "uint256", "name": "fromAccountBalance"}, {"type": "uint256", "name": "toAccountBalance"}, {"type": "uint256", "name": "businessZoneBalance"}, {"type": "uint16", "name": "bizZoneId"}, {"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "string", "name": "fromAccountName"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "string", "name": "toAccountName"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}]}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "addToken", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "addTotalSupply", "constant": false, "payable": false, "inputs": [{"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "approve", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "burn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "burnCancel", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "uint256", "name": "blockTimestamp"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "checkApprove", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes", "name": "accountSignature"}, {"type": "bytes", "name": "info"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "customTransfer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}, {"type": "bytes32", "name": "traceId"}], "outputs": [{"type": "bool", "name": "result"}]}, {"type": "function", "name": "getAllowance", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}], "outputs": [{"type": "uint256", "name": "allowance"}, {"type": "uint256", "name": "approvedAt"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAllowanceList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "ownerAccountId"}, {"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}], "outputs": [{"type": "tuple[]", "name": "approvalData", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getBalanceList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "uint16[]", "name": "zoneIds"}, {"type": "string[]", "name": "zoneNames"}, {"type": "uint256[]", "name": "balances"}, {"type": "string[]", "name": "accountNames"}, {"type": "bytes32[]", "name": "accountStatus"}, {"type": "uint256", "name": "totalBalance"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getTokenAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "tuple", "name": "token", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}]}]}, {"type": "function", "name": "hasToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bool", "name": "chkEnabled"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "hasTokenState", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "mint", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "modToken", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setTokenAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "token", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setTokenEnabled", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "bytes32", "name": "tokenId"}, {"type": "bool", "name": "enabled"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "subTotalSupply", "constant": false, "payable": false, "inputs": [{"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "transferSingle", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0xaa0f70eca845a4becc5b8a36100a2f241d8aa920bb9b3dbc0dc90e46996ea0b0", "receipt": {"to": null, "from": "0xd04A5335ac4F52269Ca796c5B19E49f420Ba1eED", "contractAddress": "0xC1ab0fA32BC8Add3E7Ab12216d631F5734E04333", "transactionIndex": 0, "gasUsed": "5527684", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x42ad9e13c03d0a9e5b4fbc3dad07143079752559860e4ff579a998b6be7f871e", "blockNumber": 75, "cumulativeGasUsed": "5527684", "status": 1}}