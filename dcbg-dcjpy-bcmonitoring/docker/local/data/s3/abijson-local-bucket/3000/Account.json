{"address": "0x2338C24B926ea767334022bA385A8F30482a27a7", "abi": [{"type": "event", "anonymous": false, "name": "Account<PERSON><PERSON>bled", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": true}, {"type": "bytes32", "name": "accountStatus", "indexed": false}, {"type": "bytes32", "name": "reasonCode", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AccountTerminated", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "reasonCode", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddAccountRole", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": true}, {"type": "address", "name": "accountEoa", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddZone", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": true}, {"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AfterBalance", "inputs": [{"type": "tuple[]", "name": "fromAfterBalance", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "uint256", "name": "balance"}]}, {"type": "tuple[]", "name": "toAfterBalance", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "uint256", "name": "balance"}]}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ForceBurn", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}, {"type": "uint256", "name": "totalBalance", "indexed": false}, {"type": "uint256", "name": "burnedAmount", "indexed": false}, {"type": "uint256", "name": "burnedBalance", "indexed": false}, {"type": "tuple[]", "name": "forceDischarge", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "uint256", "name": "dischargeAmount"}]}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "addAccount", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "validatorId"}], "outputs": []}, {"type": "function", "name": "addAccountRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "address", "name": "accountEoa"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "addZone", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "approve", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}, {"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "balanceOf", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "uint256", "name": "balance"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "burn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "uint256", "name": ""}]}, {"type": "function", "name": "calcAllowance", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}, {"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "calcBalance", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "fromA<PERSON>unt"}, {"type": "bytes32", "name": "toAccount"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "uint256", "name": "fromAccountBalance"}, {"type": "uint256", "name": "toAccountBalance"}]}, {"type": "function", "name": "editBalance", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "uint256", "name": "calcPattern"}], "outputs": [{"type": "uint256", "name": "balance"}]}, {"type": "function", "name": "emitAfterBalance", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "forceBurn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "getAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountData", "components": [{"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountDataAll", "components": [{"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "dischargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}, {"type": "tuple", "name": "cumulativeTransactionLimits", "components": [{"type": "uint256", "name": "cumulativeMintLimit"}, {"type": "uint256", "name": "cumulativeMintAmount"}, {"type": "uint256", "name": "cumulativeBurnLimit"}, {"type": "uint256", "name": "cumulativeBurnAmount"}, {"type": "uint256", "name": "cumulativeChargeLimit"}, {"type": "uint256", "name": "cumulativeChargeAmount"}, {"type": "uint256", "name": "cumulativeDischargeLimit"}, {"type": "uint256", "name": "cumulativeDischargeAmount"}, {"type": "uint256", "name": "cumulativeTransferLimit"}, {"type": "uint256", "name": "cumulativeTransferAmount"}]}, {"type": "tuple[]", "name": "businessZoneAccounts", "components": [{"type": "string", "name": "accountName"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountCount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": "count"}]}, {"type": "function", "name": "getAccountId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountLimit", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountLimitData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "dischargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}, {"type": "tuple", "name": "cumulativeTransactionLimits", "components": [{"type": "uint256", "name": "cumulativeMintLimit"}, {"type": "uint256", "name": "cumulativeMintAmount"}, {"type": "uint256", "name": "cumulativeBurnLimit"}, {"type": "uint256", "name": "cumulativeBurnAmount"}, {"type": "uint256", "name": "cumulativeChargeLimit"}, {"type": "uint256", "name": "cumulativeChargeAmount"}, {"type": "uint256", "name": "cumulativeDischargeLimit"}, {"type": "uint256", "name": "cumulativeDischargeAmount"}, {"type": "uint256", "name": "cumulativeTransferLimit"}, {"type": "uint256", "name": "cumulativeTransferAmount"}]}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountsAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "tuple", "name": "account", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint16[]", "name": "zoneIds"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "bytes32", "name": "validatorId"}, {"type": "bool", "name": "accountIdExistence"}, {"type": "address", "name": "accountEoa"}, {"type": "tuple[]", "name": "accountApprovalAll", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}]}]}, {"type": "function", "name": "getAllowance", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}], "outputs": [{"type": "uint256", "name": "allowance"}, {"type": "uint256", "name": "approvedAt"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAllowanceList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "ownerId"}, {"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}], "outputs": [{"type": "tuple[]", "name": "approvalData", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getDestinationAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "string", "name": "accountName"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getValidatorIdByAccountId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getZoneByAccountId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple[]", "name": "zones", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "bytes32[]", "name": "availableIssuerIds"}]}]}, {"type": "function", "name": "hasAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "isActivated", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "isFrozen", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "frozen"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "isTerminated", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "terminated"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "mint", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "uint256", "name": ""}]}, {"type": "function", "name": "modAccount", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "partialForceBurn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "burnedAmount"}, {"type": "uint256", "name": "burnedBalance"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setAccountAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "account", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint16[]", "name": "zoneIds"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "bytes32", "name": "validatorId"}, {"type": "bool", "name": "accountIdExistence"}, {"type": "address", "name": "accountEoa"}, {"type": "tuple[]", "name": "accountApprovalAll", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setAccountStatus", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setTerminated", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x14f334c1c01540c01cccc1dea612e4926287abd502e194b8cee6a286f88706ab", "receipt": {"to": null, "from": "0xd04A5335ac4F52269Ca796c5B19E49f420Ba1eED", "contractAddress": "0x2338C24B926ea767334022bA385A8F30482a27a7", "transactionIndex": 0, "gasUsed": "6585798", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x6b23ca4619202a4e923ba2cbc61e2b5882625e065b853238d633397c1d08e6e8", "blockNumber": 62, "cumulativeGasUsed": "6585798", "status": 1}}