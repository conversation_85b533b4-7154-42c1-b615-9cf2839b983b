#!/usr/bin/env bash

LOCALSTACK_HOST=localhost
AWS_REGION=ap-northeast-1

echo "Start to configure LocalStack."

awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
  s3 mb s3://abijson-local-bucket

awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
  s3 cp /tmp/data s3://abijson-local-bucket \
  --recursive

awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
  dynamodb create-table \
  --region ${AWS_REGION} \
  --table-name local-BlockHeight \
  --billing-mode PAY_PER_REQUEST \
  --attribute-definitions \
      AttributeName=id,AttributeType=N \
  --key-schema \
      AttributeName=id,KeyType=HASH

awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
  dynamodb create-table \
  --region ${AWS_REGION} \
  --table-name local-Events \
  --billing-mode PAY_PER_REQUEST \
  --attribute-definitions \
      AttributeName=transactionHash,AttributeType=S \
      AttributeName=logIndex,AttributeType=N \
  --key-schema \
      AttributeName=transactionHash,KeyType=HASH \
      AttributeName=logIndex,KeyType=RANGE \

awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
dynamodb create-table \
--region ${AWS_REGION} \
--table-name local-BlockHeight_Java \
--billing-mode PAY_PER_REQUEST \
--attribute-definitions \
AttributeName=id,AttributeType=N \
--key-schema \
AttributeName=id,KeyType=HASH

awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
dynamodb create-table \
--region ${AWS_REGION} \
--table-name local-Events_Java \
--billing-mode PAY_PER_REQUEST \
--attribute-definitions \
AttributeName=transactionHash,AttributeType=S \
AttributeName=logIndex,AttributeType=N \
--key-schema \
AttributeName=transactionHash,KeyType=HASH \
AttributeName=logIndex,KeyType=RANGE \

echo "Finished configuring LocalStack."
